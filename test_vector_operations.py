#!/usr/bin/env python3
"""
测试向量数据库的CRUD操作
"""
import asyncio
import os
from dotenv import load_dotenv
from app.core.vectordb import MilvusVectorDB

# 加载环境变量
load_dotenv()

# 获取Milvus连接参数
uri = os.getenv("VECTOR_DB_URI", "")
token = os.getenv("VECTOR_DB_TOKEN", "")

async def test_vector_operations():
    print("开始测试向量数据库CRUD操作...")
    
    # 测试数据库和集合
    database = "test_crud_db"
    collection = "test_crud_collection"
    
    # 创建MilvusVectorDB实例
    db = MilvusVectorDB(uri=uri, token=token, database=database)
    
    try:
        # 1. 连接到Milvus
        await db.connect()
        print(f"✓ 成功连接到Milvus数据库: {database}")
        
        # 2. 创建集合
        await db.create_collection(collection, 5)  # 5维向量
        print(f"✓ 成功创建集合: {collection}")
        
        # 3. 测试插入操作（带自定义ID）
        print("\n=== 测试插入操作 ===")
        insert_records = [
            {
                "id": 1,
                "vector": [0.1, 0.2, 0.3, 0.4, 0.5],
                "content": "测试文档1",
                "metadata": {"category": "test", "type": "document"},
                "custom_field": "value1"
            },
            {
                "id": 2,
                "vector": [0.2, 0.3, 0.4, 0.5, 0.6],
                "content": "测试文档2",
                "metadata": {"category": "test", "type": "document"},
                "custom_field": "value2"
            },
            {
                "id": 3,
                "vector": [0.3, 0.4, 0.5, 0.6, 0.7],
                "content": "测试文档3",
                "metadata": {"category": "demo", "type": "document"},
                "custom_field": "value3"
            }
        ]
        
        # 注意：当前的insert_vectors方法不支持自定义ID，因为使用了auto_id=True
        # 我们需要先测试不带ID的插入
        insert_records_no_id = [
            {
                "vector": [0.1, 0.2, 0.3, 0.4, 0.5],
                "content": "测试文档1",
                "metadata": {"category": "test", "type": "document"},
                "custom_field": "value1"
            },
            {
                "vector": [0.2, 0.3, 0.4, 0.5, 0.6],
                "content": "测试文档2",
                "metadata": {"category": "test", "type": "document"},
                "custom_field": "value2"
            },
            {
                "vector": [0.3, 0.4, 0.5, 0.6, 0.7],
                "content": "测试文档3",
                "metadata": {"category": "demo", "type": "document"},
                "custom_field": "value3"
            }
        ]
        
        result = await db.insert_vectors(collection, insert_records_no_id)
        print(f"✓ 成功插入 {result} 条记录")
        
        # 4. 测试搜索操作
        print("\n=== 测试搜索操作 ===")
        search_vector = [0.15, 0.25, 0.35, 0.45, 0.55]
        search_results = await db.search_vectors(collection, search_vector, 2)
        print(f"✓ 搜索返回 {len(search_results)} 条结果")
        for i, result in enumerate(search_results):
            print(f"  结果 {i+1}: 内容='{result.get('content', '')}', 相似度={result.get('distance', 0):.3f}")
        
        # 5. 测试Upsert操作
        print("\n=== 测试Upsert操作 ===")
        # 对于upsert，我们需要知道具体的ID，但由于使用了auto_id，我们无法预知ID
        # 这里我们演示upsert的概念，实际使用时需要先查询获取ID
        try:
            upsert_records = [
                {
                    "id": 1,  # 假设这个ID存在
                    "vector": [0.11, 0.21, 0.31, 0.41, 0.51],
                    "content": "更新后的测试文档1",
                    "metadata": {"category": "updated", "type": "document"},
                    "custom_field": "updated_value1"
                }
            ]
            result = await db.upsert_vectors(collection, upsert_records)
            print(f"✓ 成功upsert {result} 条记录")
        except Exception as e:
            print(f"⚠ Upsert操作失败（预期的，因为当前集合使用auto_id）: {e}")
        
        # 6. 测试删除操作
        print("\n=== 测试删除操作 ===")
        
        # 按条件删除
        try:
            delete_count = await db.delete_vectors(collection, filter_expr="custom_field == 'value3'")
            print(f"✓ 按条件删除了 {delete_count} 条记录")
        except Exception as e:
            print(f"⚠ 按条件删除失败: {e}")
        
        # 按ID删除（需要知道具体的ID）
        try:
            delete_count = await db.delete_vectors(collection, ids=[1, 2])
            print(f"✓ 按ID删除了 {delete_count} 条记录")
        except Exception as e:
            print(f"⚠ 按ID删除失败: {e}")
        
        # 7. 最终搜索验证
        print("\n=== 验证删除结果 ===")
        final_results = await db.search_vectors(collection, search_vector, 10)
        print(f"✓ 删除后剩余 {len(final_results)} 条记录")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n测试完成")

if __name__ == "__main__":
    asyncio.run(test_vector_operations())
